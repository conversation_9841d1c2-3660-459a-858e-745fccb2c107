#!/bin/bash

# Development environment startup script
# This script starts both frontend and backend with PM2 for persistent development

echo "🚀 Starting development environment..."

# Check if PM2 is installed
if ! command -v pm2 &> /dev/null; then
    echo "❌ PM2 is not installed. Installing PM2..."
    npm install -g pm2
fi

# Stop any existing dev processes
echo "🧹 Cleaning up existing processes..."
pm2 stop backend frontend 2>/dev/null || true
pm2 delete backend frontend 2>/dev/null || true

# Start backend
echo "🔧 Starting backend (API)..."
pm2 start "npm run dev:api" --name backend

# Start frontend
echo "🎨 Starting frontend (UI)..."
pm2 start "npm run dev:ui" --name frontend

# Show status
echo "✅ Development environment started!"
echo ""
echo "📊 Process Status:"
pm2 list

echo ""
echo "🌐 URLs:"
echo "  Frontend: http://localhost:5173"
echo "  Backend:  http://localhost:3000"
echo ""
echo "📝 Useful commands:"
echo "  pm2 logs          # View all logs"
echo "  pm2 logs backend  # Backend logs only"
echo "  pm2 logs frontend # Frontend logs only"
echo "  pm2 stop all      # Stop all processes"
echo "  pm2 restart all   # Restart all processes"
echo ""
echo "🎯 Both services have hot reload enabled!"
