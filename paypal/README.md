# PayPal Remote MCP Server Production Setup

This directory contains the configuration for connecting to PayPal's remote MCP (Model Context Protocol) server in production.

## Quick Setup

### 1. Configure <PERSON>op

The `claude_desktop_config.json` file is already configured to connect to PayPal's remote MCP server:

```json
{
  "mcpServers": {
    "paypal-mcp-server": {
      "command": "npx",
      "args": [
        "mcp-remote",
        "https://mcp.paypal.com/http"
      ]
    }
  }
}
```

### 2. Install Configuration

Copy the configuration to your Claude Desktop config location:

**macOS:**
```bash
cp claude_desktop_config.json ~/Library/Application\ Support/Claude/claude_desktop_config.json
```

**Windows:**
```bash
copy claude_desktop_config.json %APPDATA%\Claude\claude_desktop_config.json
```

**Linux:**
```bash
cp claude_desktop_config.json ~/.config/Claude/claude_desktop_config.json
```

### 3. Restart <PERSON> and Authenticate

1. **Restart <PERSON>** to load the PayPal MCP server configuration
2. **PayPal Login Page** will appear automatically
3. **Log into PayPal** when prompted
4. **Authorize the client** to work with the MCP server
5. **Quit and reopen** Claude Desktop to complete setup

## Available Tools

The PayPal remote MCP server provides tools for:
- Payment processing
- Order management  
- Subscription handling
- Transaction queries
- Webhook management

## Authentication Flow

The remote MCP server uses OAuth authentication:
- No pre-generated tokens required
- Browser-based login and authorization
- Secure OAuth 2.0 flow managed by PayPal

## Troubleshooting

1. **Connection issues**: Verify network access to `https://mcp.paypal.com`
2. **Authentication problems**: Clear auth cache with `rm -rf ~/.mcp-auth`
3. **Login issues**: Try quitting and reopening Claude Desktop
4. **Permission errors**: Re-authorize through the login flow