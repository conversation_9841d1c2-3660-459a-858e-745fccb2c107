#!/usr/bin/env node

/**
 * PayPal Remote MCP Server Setup Verification
 * 
 * This script verifies the PayPal remote MCP server setup by:
 * 1. Checking configuration file exists
 * 2. Testing mcp-remote package availability
 * 3. Providing next steps for completion
 */

const fs = require('fs');
const { spawn } = require('child_process');

console.log('🧪 PayPal Remote MCP Server Setup Verification...\n');

// Check configuration file
console.log('📋 Checking Configuration...');
if (fs.existsSync('./claude_desktop_config.json')) {
  const config = JSON.parse(fs.readFileSync('./claude_desktop_config.json', 'utf8'));
  if (config.mcpServers && config.mcpServers['paypal-mcp-server']) {
    console.log('✅ Configuration file exists and is properly formatted');
    console.log(`   Server endpoint: ${config.mcpServers['paypal-mcp-server'].args[1]}`);
  } else {
    console.log('❌ Configuration file exists but has incorrect format');
  }
} else {
  console.log('❌ Configuration file not found');
}

console.log('\n🔍 Testing mcp-remote package availability...');

// Test if mcp-remote package can be found
const mcpCheck = spawn('npm', ['list', '-g', 'mcp-remote'], {
  stdio: ['ignore', 'pipe', 'pipe']
});

let packageFound = false;

mcpCheck.stdout.on('data', (data) => {
  if (data.toString().includes('mcp-remote')) {
    packageFound = true;
    console.log('✅ mcp-remote package is available globally');
  }
});

mcpCheck.on('close', (code) => {
  if (!packageFound) {
    console.log('⚠️  mcp-remote not found globally, will be installed when needed');
  }
  
  console.log('\n🚀 Setup Complete! Next Steps:');
  console.log('   1. Copy claude_desktop_config.json to your Claude Desktop config location:');
  console.log('      macOS: ~/Library/Application\\ Support/Claude/claude_desktop_config.json');
  console.log('      Windows: %APPDATA%\\Claude\\claude_desktop_config.json');
  console.log('      Linux: ~/.config/Claude/claude_desktop_config.json');
  console.log('   2. Restart Claude Desktop');
  console.log('   3. PayPal login page will appear - complete OAuth authentication');
  console.log('   4. Quit and reopen Claude Desktop to finalize setup');
  console.log('\n📖 For detailed instructions, see README.md');
});