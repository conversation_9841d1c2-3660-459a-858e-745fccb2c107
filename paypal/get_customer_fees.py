import requests
import os
from datetime import datetime, timedelta
from dotenv import load_dotenv

# Load PayPal credentials from .env
load_dotenv('../.env')

CLIENT_ID = os.getenv('PAYPAL_CLIENT_ID')
CLIENT_SECRET = os.getenv('PAYPAL_SECRET_KEY')

# Toggle between sandbox and production
USE_SANDBOX = False  # Set to False for production
BASE_URL = 'https://api-m.sandbox.paypal.com' if USE_SANDBOX else 'https://api-m.paypal.com'

print(f"Using PayPal URL: {BASE_URL}")
print(f"Client ID: {CLIENT_ID[:10]}..." if CLIENT_ID else "Client ID not found")
print(f"Secret Key: {'*' * 10 if CLIENT_SECRET else 'Secret Key not found'}")

def get_access_token():
    """Get PayPal OAuth token"""
    auth = (CLIENT_ID, CLIENT_SECRET)
    headers = {'Accept': 'application/json', 'Accept-Language': 'en_US'}
    data = {'grant_type': 'client_credentials'}
    
    response = requests.post(
        f'{BASE_URL}/v1/oauth2/token',
        headers=headers,
        data=data,
        auth=auth
    )
    
    if response.status_code == 200:
        return response.json()['access_token']
    else:
        print(f"Auth error: {response.status_code} - {response.text}")
        return None

def search_transactions_by_email(email, access_token):
    """Search transactions by customer email"""
    end_date = datetime.utcnow()
    start_date = end_date - timedelta(days=30)  # Last 30 days
    
    params = {
        'transaction_info': 'paypal_payer_email',
        'email_address': email,
        'start_date': start_date.strftime('%Y-%m-%dT%H:%M:%SZ'),
        'end_date': end_date.strftime('%Y-%m-%dT%H:%M:%SZ'),
        'fields': 'all'
    }
    
    headers = {
        'Authorization': f'Bearer {access_token}',
        'Content-Type': 'application/json'
    }
    
    response = requests.get(
        f'{BASE_URL}/v1/reporting/transactions',
        headers=headers,
        params=params
    )
    
    if response.status_code == 200:
        return response.json().get('transaction_details', [])
    else:
        print(f"Search error: {response.status_code} - {response.text}")
        return []

def get_capture_fee(capture_id, access_token):
    """Get fee for a specific capture"""
    headers = {
        'Authorization': f'Bearer {access_token}',
        'Content-Type': 'application/json'
    }
    
    response = requests.get(
        f'{BASE_URL}/v2/payments/captures/{capture_id}',
        headers=headers
    )
    
    if response.status_code == 200:
        data = response.json()
        if 'seller_receivable_breakdown' in data:
            fee = data['seller_receivable_breakdown'].get('paypal_fee', {}).get('value', '0')
            return float(fee)
    return 0

def main():
    customer_email = '<EMAIL>'
    
    # Get access token
    print("Getting PayPal access token...")
    token = get_access_token()
    if not token:
        print("Failed to get access token")
        return
    
    # Search transactions
    print(f"\nSearching transactions for {customer_email}...")
    transactions = search_transactions_by_email(customer_email, token)
    
    if not transactions:
        print("No transactions found for this email in the last 30 days")
        return
    
    print(f"\nFound {len(transactions)} transaction(s)")
    
    total_fees = 0
    for tx in transactions:
        tx_info = tx.get('transaction_info', {})
        capture_id = tx_info.get('transaction_id')
        amount = tx_info.get('transaction_amount', {}).get('value', '0')
        date = tx_info.get('transaction_initiation_date', '')
        status = tx_info.get('transaction_status', '')
        
        print(f"\n--- Transaction ---")
        print(f"Date: {date}")
        print(f"Amount: ${amount}")
        print(f"Status: {status}")
        print(f"Transaction ID: {capture_id}")
        
        if capture_id and status == 'S':  # S = Success
            fee = get_capture_fee(capture_id, token)
            print(f"PayPal Fee: ${fee:.2f}")
            total_fees += fee
    
    print(f"\n===================")
    print(f"Total PayPal Fees: ${total_fees:.2f}")

if __name__ == "__main__":
    main()